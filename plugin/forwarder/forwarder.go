package forwarder

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"strings"
	"sync"
	"time"

	"github.com/robsonrebeloreis/whatsapp-go-bot/handler"
	"github.com/robsonrebeloreis/whatsapp-go-bot/multi_session"
	"github.com/robsonrebeloreis/whatsapp-go-bot/pkg/msglog"
	"github.com/robsonrebeloreis/whatsapp-go-bot/pkg/policy"
	"github.com/robsonrebeloreis/whatsapp-go-bot/plugin"
	"github.com/robsonrebeloreis/whatsapp-go-bot/plugin/interactive"
	"github.com/robsonrebeloreis/whatsapp-go-bot/utils/logger"
	"github.com/robsonrebeloreis/whatsapp-go-bot/utils/ratelimit"

	"go.mau.fi/whatsmeow"
	waProto "go.mau.fi/whatsmeow/binary/proto"
	"go.mau.fi/whatsmeow/types"
	"go.mau.fi/whatsmeow/types/events"
	"google.golang.org/protobuf/proto"
)

// StringPtr returns a pointer to the given string
func StringPtr(s string) *string {
	return &s
}

// ResponseData represents the JSON response structure
type ResponseData struct {
	Text    string          `json:"text"`
	Details []ProductDetail `json:"details"`
	Lists   *ListsResponse  `json:"lists"`
}

// ListsResponse represents a list response from the service
type ListsResponse struct {
	Title       string      `json:"title"`
	Subtitle    string      `json:"subtitle"`
	Description string      `json:"description"`
	Menu        string      `json:"menu"`
	List        []ListGroup `json:"list"`
}

// ListGroup represents a group in a list
type ListGroup struct {
	Title string     `json:"title"`
	Rows  []ListItem `json:"rows"`
}

// ListItem represents an item in a list group
type ListItem struct {
	RowID       string `json:"rowId"`
	Title       string `json:"title"`
	Description string `json:"description"`
}

// ProductDetail represents a product in the details array
type ProductDetail struct {
	Image ImageInfo `json:"image"`
	Desc  DescInfo  `json:"desc"`
}

// ImageInfo contains information about the product image
type ImageInfo struct {
	Nome string `json:"nome"`
	URL  string `json:"url"`
	Text string `json:"text"`
}

// DescInfo contains the product description
type DescInfo struct {
	Text string `json:"text"`
}

// AIHandler interface for AI processing
type AIHandler interface {
	ProcessMessage(ctx context.Context, chatJID types.JID, sender types.JID, message string) error
	IsEnabled() bool
}

// ServiceResponse represents the response from the external service
type ServiceResponse struct {
	Content string `json:"content"` // Campo para receber o conteúdo da resposta do midd-bot
	Details []struct {
		Image struct {
			Nome string `json:"nome"`
			URL  string `json:"url"`
			Text string `json:"text"`
		} `json:"image"`
		Desc struct {
			Text string `json:"text"`
		} `json:"desc"`
	} `json:"details"`
	Lists    *ListsResponse         `json:"lists"`
	Metadata map[string]interface{} `json:"metadata"` // Campo para armazenar metadados da resposta
}

// ForwarderPlugin forwards WhatsApp messages to a specified URL
type ForwarderPlugin struct {
	*plugin.BasePlugin
	mediaHandler      *handler.MediaHandler
	forwardURL        string
	httpClient        *http.Client
	rateLimiter       *ratelimit.RateLimiter
	interactivePlugin *interactive.InteractivePlugin
	aiHandler         AIHandler
	policy            *policy.ForwarderPolicy
	whatsapp          *whatsmeow.Client
	msgLogger         msglog.Logger // Message logger for storing messages

	// For handling list selections
	selectionHandlers     map[string]map[string]string // map[chatJID]map[selectionKey]rowID
	selectionHandlerMutex sync.RWMutex
}

// NewForwarderPlugin creates a new forwarder plugin
func NewForwarderPlugin(forwardURL string) *ForwarderPlugin {
	// Initialize the plugin
	p := &ForwarderPlugin{
		BasePlugin: plugin.NewBasePlugin("forwarder"),
		forwardURL: forwardURL,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		rateLimiter:       nil,
		selectionHandlers: make(map[string]map[string]string),
		policy: &policy.ForwarderPolicy{
			Name:     "forwarder",
			Enabled:  true,
			Priority: 1,
			Endpoint: forwardURL,
			Method:   "GET",
			Headers: map[string]string{
				"Content-Type": "application/json",
			},
			Retry: policy.RetryConfig{
				MaxAttempts:  3,
				InitialDelay: "1s",
				MaxDelay:     "5s",
			},
			Conditions: []policy.Condition{
				{Type: StringPtr("text")},
				{Type: StringPtr("image")},
				{Type: StringPtr("video")},
			},
			ForwardingRules: []policy.ForwardingRule{
				{
					Target: "chatbot",
					URL:    forwardURL,
					Conditions: []policy.Condition{
						{Type: StringPtr("text")},
						{Type: StringPtr("image")},
						{Type: StringPtr("video")},
					},
				},
			},
		},
	}

	// Initialize message logger com mais logs de depuração
	fmt.Printf("Inicializando message logger a partir de config/msglog.json\n")
	msgLogger, err := msglog.GetLogger("config/msglog.json")
	if err != nil {
		fmt.Printf("ERRO: Falha ao inicializar message logger: %v\n", err)
		// Tentativa de verificar o arquivo de configuração
		if _, fileErr := os.Stat("config/msglog.json"); fileErr != nil {
			fmt.Printf("ERRO: Arquivo de configuração não encontrado: %v\n", fileErr)
		} else {
			fmt.Printf("Arquivo de configuração existe, mas houve erro ao carregar\n")
		}
	} else {
		p.msgLogger = msgLogger
		fmt.Printf("SUCESSO: Message logger inicializado com sucesso\n")

		// Teste de conexão com o banco de dados
		testMsg := &msglog.Message{
			SessionID:   "test-session",
			UserID:      "test-user",
			Content:     "Mensagem de teste do logger",
			Direction:   msglog.DirectionOutgoing,
			Timestamp:   time.Now(),
			MessageType: "text",
			Metadata:    "{\"test\": true}",
		}

		testErr := msgLogger.LogMessage(context.Background(), testMsg)
		if testErr != nil {
			fmt.Printf("ERRO: Falha no teste de gravação de mensagem: %v\n", testErr)
		} else {
			fmt.Printf("SUCESSO: Teste de gravação de mensagem bem-sucedido com ID: %s\n", testMsg.ID)
		}
	}

	return p
}

// Name returns the name of the plugin
func (p *ForwarderPlugin) Name() string {
	return p.BasePlugin.GetName()
}

// SetRateLimiter sets the rate limiter for the plugin
func (p *ForwarderPlugin) SetRateLimiter(limiter *ratelimit.RateLimiter) {
	p.rateLimiter = limiter
}

// Start starts the plugin
func (p *ForwarderPlugin) Start(ctx context.Context) error {
	return nil
}

// Stop stops the plugin
func (p *ForwarderPlugin) Stop(ctx context.Context) error {
	return nil
}

// Pause pauses the plugin
func (p *ForwarderPlugin) Pause(ctx context.Context) error {
	return nil
}

// Resume resumes the plugin
func (p *ForwarderPlugin) Resume(ctx context.Context) error {
	return nil
}

// GetState returns the current state of the plugin
func (p *ForwarderPlugin) GetState() plugin.PluginState {
	return plugin.StateRunning
}

// Description returns a description of the plugin
func (p *ForwarderPlugin) Description() string {
	return "Forwards WhatsApp messages to a specified URL"
}

// Initialize initializes the plugin with the WhatsApp client
func (p *ForwarderPlugin) Initialize(client interface{}, commandHandler interface{}) error {
	if whatsappClient, ok := client.(*whatsmeow.Client); ok {
		p.whatsapp = whatsappClient
		logger.Info("WhatsApp client initialized for forwarder plugin")
	} else {
		logger.Error("Failed to initialize WhatsApp client: invalid client type")
		return fmt.Errorf("invalid client type")
	}
	return nil
}

// SetAIHandler sets the AI handler
func (p *ForwarderPlugin) SetAIHandler(aiHandler AIHandler) {
	p.aiHandler = aiHandler
	fmt.Printf("AI handler set for forwarder plugin, enabled: %v\n", aiHandler.IsEnabled())
}

// HandleMessage is called for each message received
func (p *ForwarderPlugin) HandleMessage(ctx context.Context, evt *events.Message) error {
	sessionID := p.whatsapp.Store.ID
	logger.Info("[Session %s] Received message: %s", sessionID, evt.Message.GetConversation())

	// Log all available message info for debugging
	logger.Info("[DEBUG] Message ID: %s", evt.Info.ID)
	logger.Info("[DEBUG] Push Name: %s", evt.Info.PushName)
	logger.Info("[DEBUG] Sender: %s", evt.Info.Sender.String())
	logger.Info("[DEBUG] Chat: %s", evt.Info.Chat.String())
	logger.Info("[DEBUG] Participant: %s", evt.Info.MessageSource.Participant.String())

	// Obter o número de telefone associado a esta sessão
	sessionInfo := p.whatsapp.Store.ID.String()
	logger.Info("[DEBUG] ID da sessão que recebeu a mensagem: %s", sessionInfo)

	// Extrair o texto da mensagem
	msgText := evt.Message.GetConversation()
	if msgText == "" {
		// Check for extended text message
		if evt.Message.ExtendedTextMessage != nil && evt.Message.ExtendedTextMessage.Text != nil {
			msgText = *evt.Message.ExtendedTextMessage.Text
		}
	}

	// Registrar mensagens enviadas pelo próprio aparelho (IsFromMe = true)
	if evt.Info.IsFromMe {
		logger.Info("[Session %s] Registrando mensagem enviada pelo próprio aparelho: %s", sessionID, msgText)

		// Log the outgoing message to our message logger if available
		if p.msgLogger != nil && msgText != "" {
			// Create metadata JSON
			metadata := map[string]interface{}{
				"chat_id":     evt.Info.Chat.String(),
				"sender":      evt.Info.Sender.String(),
				"push_name":   evt.Info.PushName,
				"is_group":    evt.Info.IsGroup,
				"timestamp":   evt.Info.Timestamp.Unix(),
				"from_device": true,
			}

			metadataJSON, _ := json.Marshal(metadata)

			// Verificar o formato do ID da sessão
			sessionIDStr := sessionID.String()
			logger.Info("[ForwarderPlugin] ID da sessão original (mensagem do aparelho): %s", sessionIDStr)

			// Extrair o número de telefone do ID da sessão
			// O formato pode ser algo como "1234567890.0:2" ou apenas "1234567890"
			phoneNumber := sessionIDStr
			if parts := strings.Split(sessionIDStr, "."); len(parts) > 0 {
				phoneNumber = parts[0]
			}
			logger.Info("[ForwarderPlugin] Número de telefone extraído (mensagem do aparelho): %s", phoneNumber)

			// Create message log entry - usar o número de telefone como ID da sessão
			msgLog := &msglog.Message{
				SessionID:   phoneNumber,
				UserID:      evt.Info.Sender.String(),
				Content:     msgText,
				Direction:   msglog.DirectionOutgoing,
				Timestamp:   evt.Info.Timestamp,
				MessageType: "text",
				Metadata:    string(metadataJSON),
			}

			// Log the message asynchronously to not block message processing
			go func() {
				if err := p.msgLogger.LogMessage(context.Background(), msgLog); err != nil {
					logger.Error("Failed to log outgoing device message: %v", err)
				} else {
					logger.Info("Successfully logged outgoing device message with ID: %s", msgLog.ID)
				}
			}()
		}

		// Não processamos mensagens enviadas pelo próprio aparelho além de registrá-las
		return nil
	}

	// O texto da mensagem já foi extraído acima

	// Skip empty messages
	if msgText == "" {
		return nil
	}

	// Skip command messages
	if strings.HasPrefix(msgText, "!") {
		return nil
	}

	// Log the message being forwarded
	logger.Info("[Session %s] Forwarding message from %s: %s", sessionID, evt.Info.Sender.String(), msgText)

	// Verifica se a mensagem é de uma newsletter
	if strings.Contains(evt.Info.Sender.String(), "@newsletter") {
		logger.Info("Ignorando mensagem de newsletter")
		return nil
	}

	// Log the incoming message to our message logger if available
	if p.msgLogger != nil {
		// Create metadata JSON
		metadata := map[string]interface{}{
			"chat_id":   evt.Info.Chat.String(),
			"sender":    evt.Info.Sender.String(),
			"push_name": evt.Info.PushName,
			"is_group":  evt.Info.IsGroup,
			"timestamp": evt.Info.Timestamp.Unix(),
		}

		metadataJSON, _ := json.Marshal(metadata)

		// Verificar o formato do ID da sessão
		sessionIDStr := sessionID.String()
		logger.Info("[ForwarderPlugin] ID da sessão original: %s", sessionIDStr)

		// Extrair o número de telefone do ID da sessão
		// O formato pode ser algo como "1234567890.0:2" ou apenas "1234567890"
		phoneNumber := sessionIDStr
		if parts := strings.Split(sessionIDStr, "."); len(parts) > 0 {
			phoneNumber = parts[0]
		}
		logger.Info("[ForwarderPlugin] Número de telefone extraído: %s", phoneNumber)

		// Create message log entry - usar o número de telefone como ID da sessão
		msgLog := &msglog.Message{
			SessionID:   phoneNumber,
			UserID:      evt.Info.Sender.String(),
			Content:     msgText,
			Direction:   msglog.DirectionIncoming,
			Timestamp:   evt.Info.Timestamp,
			MessageType: "text",
			Metadata:    string(metadataJSON),
		}

		// Log the message asynchronously to not block message processing
		go func() {
			if err := p.msgLogger.LogMessage(context.Background(), msgLog); err != nil {
				logger.Error("Failed to log incoming message: %v", err)
			} else {
				logger.Info("Successfully logged incoming message with ID: %s", msgLog.ID)
			}
		}()
	}

	// Create a policy message
	msg := &policy.Message{
		Chat:     evt.Info.Chat,
		Sender:   evt.Info.Sender,
		Content:  msgText,
		Type:     "text",
		Metadata: make(map[string]interface{}),
	}

	// Check if the message has sender_pn in the push name
	if evt.Info.PushName != "" {
		logger.Info("Message has push name: %s", evt.Info.PushName)
	}

	// Check if this is a message from a mobile phone with @lid suffix
	senderStr := evt.Info.Sender.String()
	if strings.HasSuffix(senderStr, "@lid") {
		logger.Info("Detected @lid sender: %s", senderStr)
		
		// Try to extract real phone number from various sources
		var realPhoneNumber string
		
		// Method 1: Check if we can get it from the message ID
		if evt.Info.ID != "" {
			parts := strings.Split(evt.Info.ID, "_")
			if len(parts) > 0 && len(parts[0]) >= 10 {
				// Message IDs often contain the real phone number
				realPhoneNumber = parts[0]
				logger.Info("Extracted phone number from message ID: %s", realPhoneNumber)
			}
		}
		
		// Method 2:  it's all digits
				isNumeric := true
				for _, r := range cleaned {
					if r < '0' || r > '9' {
						isNumeric = false
						break
					}
				}
				if isNumeric {
					realPhoneNumber = cleaned
					logger.Info("Extracted phone number from push name: %s", realPhoneNumber)
				}
			}
		}
		
		// Method 3: Check if we have the real number in contacts
		if realPhoneNumber == "" && p.whatsapp != nil && p.whatsapp.Store != nil {
			contacts, err := p.whatsapp.Store.Contacts.GetAllContacts(context.Background())
			if err == nil {
				logger.Info("Searching in %d contacts for real number", len(contacts))
				lidNumber := strings.Split(senderStr, "@")[0]
				
				for jid, contact := range contacts {
					// Look for the real number that might be associated with this @lid
					if jid.3erver == "s.whatsapp.net" {
						// Check if this contact might be the real sender
						if contact.PushName != "" && strings.Contains(evt.Info.PushName, contact.PushName) {
							realPhoneNumber = jid.User
							logger.Info("Found real number in contacts by push name match: %s", realPhoneNumber)
							break
						}
					}
				}
				
				// Use lidNumber in the loop above to avoid "declared and not used" error
				_ = lidNumber
			}
		}
		
		// Method 4: Hardcoded mapping for known cases (temporary solution)
		if realPhoneNumber == "" {
			//
				
				// Use lidNumber in the loop above to avoid "declared and not used" error
				_ = lidNumber You can add known mappings here
			knownMappings := map[string]string{
				"157019961090264": "5511978884508", // Add your known mappings
			}
			4
			lidNumber := strings.Split(senderStr, "@")[0]
			if mapped, exists := knownMappings[lidNumber]; exists {
				realPhoneNumber = mapped
				logger.Info("Using hardcoded mapping: %s -> %s", lidNumber, realPhoneNumber)
			}
		}
		
		// If we found a real phone number, store it in metadata
		if realPhoneNumber != "" {
			// Ensure it has country code format
			if !strings.HasPrefix(realPhoneNumber, "55") && len(realPhoneNumber) == 11 {
				realPhoneNumber = "55" + realPhoneNumber
			}
			msg.Metadata["sender_pn"] = realPhoneNumber + "@s.whatsapp.net"
			msg.Metadata["real_sender_number"] = realPhoneNumber
			logger.Info("REAL SENDER NUMBER FOUND: %s (was @lid: %s)", realPhoneNumber, senderStr)
		} else {
			logger.Warning("Could not extract real phone number from @lid JID: %s", senderStr)
			logger.Warning("Message ID: %s, Push Name: %s", evt.Info.ID, evt.Info.PushName)
		}
	}

	// Adicionar um marcador nos metadados para rastrear a origem da mensagem
	msg.Metadata["origin"] = "HandleMessage"

	// Chamar diretamente a função Handle para processar a mensagem
	// Isso evita a duplicação de processamento
	if err := p.Handle(ctx, msg); err != nil {
		logger.Error("[Session %s] Failed to process message: %v", sessionID, err)
		return fmt.Errorf("failed to process message: %v", err)
	}

	logger.Info("[Session %s] Successfully processed message and sent response", sessionID)
	return nil
}

// Shutdown is called when the bot is shutting down
func (p *ForwarderPlugin) Shutdown() error {
	// Close message logger if available
	if p.msgLogger != nil {
		if err := p.msgLogger.Close(); err != nil {
			logger.Error("Failed to close message logger: %v", err)
		}
	}

	return nil
}

// This GetPolicy implementation has been removed to avoid duplication
// The implementation at line 801 will be used instead

// min returns the smaller of x or y
func min(x, y int) int {
	if x < y {
		return x
	}
	return y
}

// isListMessage checks if the response is a list message
func isListMessage(responseBody string) bool {
	// Print the first 200 characters of the response body for debugging
	fmt.Printf("[DEBUG] Response body starts with: %s\n", responseBody[:min(200, len(responseBody))])

	// Check if the response body contains the lists field
	hasLists := strings.Contains(responseBody, "\"lists\":") || strings.Contains(responseBody, "{\"lists\":")
	fmt.Printf("[DEBUG] Has lists field: %v\n", hasLists)

	return len(responseBody) > 0 && responseBody[0] == '{' && hasLists
}

// Handle implements the policy.MessageHandler interface
func (p *ForwarderPlugin) Handle(ctx context.Context, msg *policy.Message) error {
	// Evita duplicidade de resposta
	if responded, ok := msg.Metadata["responded"].(bool); ok && responded {
		logger.Info("Mensagem já respondida por outro handler, ignorando.")
		return nil
	}
	// Log da mensagem completa para depuração
	logger.Info("[DEBUG] ForwarderPlugin.Handle - Mensagem completa recebida: %+v", msg)
	logger.Info("[DEBUG] ForwarderPlugin.Handle - Conteúdo da mensagem: '%s'", msg.Content)
	logger.Info("[DEBUG] ForwarderPlugin.Handle - Remetente: %s, Chat: %s", msg.Sender.String(), msg.Chat.String())

	// Skip command messages (starting with !)
	if strings.HasPrefix(msg.Content, "!") {
		return nil
	}

	// Determinar qual cliente WhatsApp usar com base no ID da sessão
	sessionID := msg.SessionID
	logger.Info("[DEBUG] ForwarderPlugin.Handle - SessionID: %s", sessionID)

	// Obter o gerenciador de sessões do contexto
	sessionManager, ok := ctx.Value("session_manager").(*multi_session.SessionManager)
	if !ok || sessionManager == nil {
		// Fallback para o cliente padrão se não houver gerenciador de sessões
		logger.Error("Session manager not found in context, using default client")
		client, ok := ctx.Value("whatsapp").(*whatsmeow.Client)
		if !ok {
			return fmt.Errorf("WhatsApp client not initialized")
		}
		p.whatsapp = client
	} else {
		// Usar o cliente específico da sessão que recebeu a mensagem
		client := sessionManager.GetSession(sessionID)
		if client == nil {
			logger.Error("Session %s not found, using default client", sessionID)
			client, ok := ctx.Value("whatsapp").(*whatsmeow.Client)
			if !ok {
				return fmt.Errorf("WhatsApp client not initialized")
			}
			p.whatsapp = client
		} else {
			logger.Info("[DEBUG] ForwarderPlugin.Handle - Using client for session: %s", sessionID)
			p.whatsapp = client
		}
	}

	// Process the forwarded message
	responseBody, err := p.forwardMessage(ctx, msg)
	if err != nil {
		logger.Error("Failed to forward message: %v", err)
		return err
	}

	// If no response or if the middleware indicated not to handle this message, just return
	if responseBody == nil || len(responseBody) == 0 {
		logger.Info("Nenhuma resposta do serviço externo, corpo vazio ou handled=false, não enviando mensagem")
		return nil
	}

	// Parse the chatbot response
	var chatbotResp ServiceResponse
	if err := json.Unmarshal(responseBody, &chatbotResp); err != nil {
		logger.Error("Failed to parse service response: %v", err)
		logger.Error("Response raw content: %s", string(responseBody))

		// Verificar se a resposta contém indicação de que não é uma consulta de produto
		var rawResponse map[string]interface{}
		if jsonErr := json.Unmarshal(responseBody, &rawResponse); jsonErr == nil {
			logger.Info("[DEBUG] Analisando metadados da resposta para decidir se deve responder")

			// Verificar metadados para decidir se deve responder
			if metadata, ok := rawResponse["metadata"].(map[string]interface{}); ok {
				// Verificar se é consulta de produto
				isProductQuery, hasProductFlag := metadata["is_product_query"].(bool)

				// Verificar se deve ser tratado pelo bot
				handled, hasHandledFlag := metadata["handled"].(bool)

				// Só responder se for consulta de produto E estiver marcado para ser tratado
				if (hasProductFlag && !isProductQuery) || (hasHandledFlag && !handled) {
					logger.Info("[DEBUG] Não respondendo: is_product_query=%v, handled=%v",
						isProductQuery, handled)
					return fmt.Errorf("mensagem não deve ser respondida conforme metadados")
				}

				// Se não tiver conteúdo para responder, não enviar mensagem
				if content, ok := rawResponse["content"].(string); ok && strings.TrimSpace(content) == "" {
					logger.Info("[DEBUG] Conteúdo vazio, não enviando resposta")
					return fmt.Errorf("conteúdo vazio")
				}
			}

			// Verificar se o intention_type indica que não é uma consulta de produto
			if intentionType, ok := rawResponse["intention_type"].(string); ok {
				if intentionType == "nao_produto" {
					logger.Info("[DEBUG] Intention type é 'nao_produto'. Não enviando resposta.")
					return fmt.Errorf("intention_type é nao_produto")
				}
			}
		}

		logger.Info("Will use direct text response instead of structured response due to parsing error")
		// Tentar criar uma estrutura básica para não interromper o fluxo
		chatbotResp = ServiceResponse{
			Content: "Desculpe, estou com dificuldades para processar sua mensagem. Por favor, tente novamente mais tarde.",
		}
	}

	// Log da resposta recebida
	logger.Info("[DEBUG] ForwarderPlugin.Handle - Recebida resposta com conteúdo: '%s'", chatbotResp.Content)

	// Enviar a resposta usando sendResponse
	if err := p.sendResponse(msg, chatbotResp); err != nil {
		logger.Error("Failed to send response: %v", err)
		return fmt.Errorf("failed to send response: %v", err)
	}
	// Marca como respondido para evitar duplicidade
	msg.Metadata["responded"] = true

	logger.Info("Successfully processed message and sent response in Handle")
	return nil
}

// forwardMessage forwards a message to the service
func (p *ForwarderPlugin) forwardMessage(ctx context.Context, msg *policy.Message) ([]byte, error) {
	chatJID := msg.Chat  // Mantido pois é usado em outras partes do código
	from := msg.Sender.String()
	message := msg.Content
	logger.Info("Building URL for request")

	// Build the URL with query parameters
	baseURL, err := url.Parse(p.forwardURL)
	if err != nil {
		logger.Error("Invalid forward URL: %v", err)
		return nil, fmt.Errorf("invalid forward URL: %v", err)
	}

	// Verifica se a mensagem é de uma newsletter
	if strings.Contains(from, "@newsletter") {
		logger.Info("Ignorando mensagem de newsletter")
		return nil, nil
	}

	// Extrair apenas o número de telefone do JID
	toNumber := from
	if strings.Contains(from, "@") {
		toNumber = strings.Split(from, "@")[0]
		// Se houver dois-pontos, pegar só a parte antes
		if strings.Contains(toNumber, ":") {
			toNumber = strings.Split(toNumber, ":")[0]
		}
	}

	// Verificar se a mensagem está vazia
	if strings.TrimSpace(message) == "" {
		logger.Warning("Mensagem vazia detectada, usando texto padrão")
		message = "[Mensagem sem conteúdo]"
	}

	// Adicionar query parameters à URL
	q := baseURL.Query()
	q.Add("send", p.whatsapp.Store.ID.User)  // bot ID
	q.Add("to", toNumber)                   // número que receberá a mensagem (apenas o número)
	q.Add("message", message)               // conteúdo da mensagem
	baseURL.RawQuery = q.Encode()

	logger.Info("URL built successfully: %s", baseURL.String())
	logger.Info("Enviando mensagem via GET com query parameters")

	// Create the request - usando GET com query parameters
	req, err := http.NewRequestWithContext(ctx, "GET", baseURL.String(), nil)
	if err != nil {
		logger.Error("Failed to create request: %v", err)
		return nil, fmt.Errorf("failed to create request: %v", err)
	}

	// Add headers
	for key, value := range p.policy.Headers {
		req.Header.Set(key, value)
		logger.Info("Added header: %s: %s", key, value)
	}
	req.Header.Set("Content-Type", "application/json")

	logger.Info("Sending request to service")

	// Send the request
	resp, err := p.httpClient.Do(req)
	if err != nil {
		logger.Error("Failed to send request: %v", err)

		// Reduzir o timeout do HTTP client para evitar longas esperas
		p.httpClient.Timeout = 10 * time.Second
		return nil, err
	}
	defer resp.Body.Close()

	logger.Info("Received response from service with status: %d", resp.StatusCode)

	// Read the response
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.Error("Failed to read response body: %v", err)
		return nil, fmt.Errorf("failed to read response: %v", err)
	}

	logger.Info("Response body: %s", string(body))

	if resp.StatusCode != http.StatusOK {
		logger.Error("Unexpected status code: %d", resp.StatusCode)

		// Tratar especificamente o código 204 No Content
		if resp.StatusCode == http.StatusNoContent {
			logger.Info("Received 204 No Content response, not sending any response to user")
			// Retornar nil para indicar que não deve enviar resposta
			return nil, nil
		}

		// Para outros códigos de erro, enviar uma mensagem de resposta padrão
		defaultResponse := "Desculpe, nosso serviço de resposta está com problemas. Por favor, tente novamente mais tarde."
		// Não envia mais diretamente via WhatsApp aqui, apenas retorna a resposta simulada.

		// Criar uma resposta simulada para não interromper o fluxo, com metadados indicando que é uma resposta válida
		// e deve ser tratada como uma resposta gerada pela IA
		simulatedResponse := []byte(`{
			"content":"` + defaultResponse + `",
			"metadata": {
				"is_product_query": false,
				"handled": true,
				"ai_generated": true,
				"error": "service_error"
			}
		}`)
		return simulatedResponse, nil
	}

	logger.Info("Successfully forwarded message and received response")

	// Parse response
	var response map[string]interface{}
	if len(body) > 0 {
		logger.Info("Response body: %s", string(body))
		if err := json.Unmarshal(body, &response); err != nil {
			logger.Error("Failed to parse service response: %v", err)
			logger.Info("Using default message due to parsing error")
			// Criar uma resposta simulada para não causar erros de parsing mais tarde, com metadados indicando que não deve responder
			body = []byte(`{
				"content":"Desculpe, não encontramos produtos que correspondam à sua consulta. Por favor, tente com outros termos.",
				"metadata": {
					"is_product_query": false,
					"handled": false,
					"error": "parsing_error"
				}
			}`)
			// Tentar fazer o parse novamente com a resposta simulada
			json.Unmarshal(body, &response)
		} else {
			// Log detalhado da resposta para diagnóstico
			logger.Info("Successfully parsed response JSON: %+v", response)
		}
	} else {
		logger.Info("Empty response body received from service")
		// Não criar resposta simulada para respostas vazias, simplesmente não responder
		logger.Info("Not sending any response for empty response body")
		return nil, nil
	}

	// Criar a estrutura ServiceResponse para retornar
	chatbotResp := ServiceResponse{
		Content: "", // Inicializar com string vazia em vez de valor padrão
	}

	if response != nil {
		// Processar metadados primeiro para diagnóstico
		if metadata, ok := response["metadata"].(map[string]interface{}); ok {
			logger.Info("Found metadata in response: %+v", metadata)

			// Extrair informações importantes dos metadados
			if aiGenerated, ok := metadata["ai_generated"].(bool); ok {
				logger.Info("AI Generated flag: %v", aiGenerated)
			}

			if handled, ok := metadata["handled"].(bool); ok {
				logger.Info("Handled flag: %v", handled)
				// Adicionar o flag handled aos metadados da resposta
				if chatbotResp.Metadata == nil {
					chatbotResp.Metadata = make(map[string]interface{})
				}
				chatbotResp.Metadata["handled"] = handled
			}

			if isProductQuery, ok := metadata["is_product_query"].(bool); ok {
				logger.Info("Is Product Query flag: %v", isProductQuery)
				// Adicionar o flag is_product_query aos metadados da resposta
				if chatbotResp.Metadata == nil {
					chatbotResp.Metadata = make(map[string]interface{})
				}
				chatbotResp.Metadata["is_product_query"] = isProductQuery
			}
		}

		if respContent, ok := response["content"].(string); ok && respContent != "" {
			// Verificar se o conteúdo é apenas um identificador ou uma resposta real
			if respContent == "general_inquiry" || respContent == "" || len(strings.TrimSpace(respContent)) < 5 {
				logger.Info("Resposta do serviço é apenas um identificador ou muito curta: '%s'. Usando resposta padrão.", respContent)
				// Manter a resposta padrão definida acima
			} else {
				chatbotResp.Content = respContent
				logger.Info("Using service response content: %s", respContent)
			}
		} else if details, ok := response["details"].([]interface{}); ok && len(details) > 0 {
			// Se não tem content mas tem details, usar o texto do primeiro detalhe
			logger.Info("No content field but found details array with %d items", len(details))

			// Processar os detalhes para a estrutura ServiceResponse
			for _, detailInterface := range details {
				if detail, ok := detailInterface.(map[string]interface{}); ok {
					var detailStruct struct {
						Image struct {
							Nome string `json:"nome"`
							URL  string `json:"url"`
							Text string `json:"text"`
						} `json:"image"`
						Desc struct {
							Text string `json:"text"`
						} `json:"desc"`
					}

					// Extrair informações da imagem
					if imageData, ok := detail["image"].(map[string]interface{}); ok {
						if text, ok := imageData["text"].(string); ok {
							detailStruct.Image.Text = text
						}
						if url, ok := imageData["url"].(string); ok {
							detailStruct.Image.URL = url
						}
						if nome, ok := imageData["nome"].(string); ok {
							detailStruct.Image.Nome = nome
						}
					}

					// Extrair informações da descrição
					if descData, ok := detail["desc"].(map[string]interface{}); ok {
						if text, ok := descData["text"].(string); ok {
							detailStruct.Desc.Text = text

							// Se ainda não temos conteúdo, usar o texto da descrição como conteúdo
							if chatbotResp.Content == "" && text != "" {
								chatbotResp.Content = text
								logger.Info("Using text from first detail as content: %s", text)
							}
						}
					}

					// Adicionar o detalhe à resposta
					chatbotResp.Details = append(chatbotResp.Details, detailStruct)
				}
			}
		} else {
			logger.Info("No valid content found in service response, using default message")
		}

		// Processar listas se existirem
		if listsData, ok := response["lists"].(map[string]interface{}); ok {
			listsResp := &ListsResponse{}

			if title, ok := listsData["title"].(string); ok {
				listsResp.Title = title
			}

			if description, ok := listsData["description"].(string); ok {
				listsResp.Description = description
			}

			if menu, ok := listsData["menu"].(string); ok {
				listsResp.Menu = menu
			}

			chatbotResp.Lists = listsResp
		}
	} else {
		logger.Info("No response object, using default message")
	}

	// Garantir que não estamos enviando apenas 'general_inquiry' como resposta
	if chatbotResp.Content == "general_inquiry" {
		chatbotResp.Content = "" // Definir como string vazia para não enviar resposta
		logger.Info("Replaced 'general_inquiry' with empty string to avoid sending response")
	}

	// Verificar se temos uma resposta válida do midd-bot
	if chatbotResp.Content == "" && chatbotResp.Metadata != nil {
		// Se não temos conteúdo mas temos metadados, verificar se devemos usar uma resposta padrão
		if handled, ok := chatbotResp.Metadata["handled"].(bool); ok && !handled {
			// Se o handled é false, não enviar resposta
			logger.Info("Metadata indicates message should not be handled (handled=false), not sending response")
			return nil, nil
		}
	}

	// Se temos conteúdo e metadados indicando que é uma resposta gerada pela IA, garantir que seja tratada como válida
	if chatbotResp.Content != "" && chatbotResp.Metadata != nil {
		if aiGenerated, ok := chatbotResp.Metadata["ai_generated"].(bool); ok && aiGenerated {
			// Garantir que a resposta seja tratada como válida
			logger.Info("Response is AI-generated, ensuring it will be handled properly")
			chatbotResp.Metadata["handled"] = true
		}
	}

	// Log da resposta final
	logger.Info("Final service response content: %s", chatbotResp.Content)
	logger.Info("Final service response metadata: %+v", chatbotResp.Metadata)

	// Check if WhatsApp client is initialized and connected
	if p.whatsapp == nil {
		logger.Error("WhatsApp client not initialized")
		return nil, fmt.Errorf("WhatsApp client not initialized")
	}

	if !p.whatsapp.IsConnected() {
		logger.Error("WhatsApp client not connected")
		return nil, fmt.Errorf("WhatsApp client not connected")
	}

	// IMPORTANTE: Usar o JID correto para enviar a resposta
	responseJID := chatJID

	// Quando o JID tem sufixo @lid, não podemos responder diretamente para ele
	// Precisamos encontrar o número de telefone real do usuário
	if strings.HasSuffix(chatJID.String(), "@lid") {
		logger.Info("Detectado JID com sufixo @lid: %s", chatJID)

		// Verificar primeiro se temos o campo sender_pn nos metadados
		// Este é o método mais confiável para obter o número real do remetente
		if senderPN, ok := msg.Metadata["sender_pn"]; ok && senderPN != "" {
			// Usar o sender_pn como JID de resposta
			senderPNStr, ok := senderPN.(string)
			if ok && senderPNStr != "" {
				// Garantir que o número tenha o formato correto para JID
				if !strings.Contains(senderPNStr, "@") {
					senderPNStr = senderPNStr + "@s.whatsapp.net"
				}
				responseJID, _ = types.ParseJID(senderPNStr)
				logger.Info("Usando sender_pn dos metadados: %s -> %s", chatJID, responseJID)
			}
		}

		// Se ainda estamos com um JID com sufixo @lid, tentar verificar o remetente da mensagem
		if strings.HasSuffix(responseJID.String(), "@lid") {
			// Fallback universal: tenta responder ao próprio sender convertido
			senderStr := msg.Sender.String()
			if strings.HasSuffix(senderStr, "@lid") {
				num := strings.Split(senderStr, "@")[0]
				senderStr = num + "@s.whatsapp.net"
			}
			fallbackJID, err := types.ParseJID(senderStr)
			if err == nil {
				logger.Info("Fallback universal: respondendo para sender convertido: %s", fallbackJID.String())
				responseJID = fallbackJID
			}
		}

		// Tentar buscar o número real nos contatos do WhatsApp
		if strings.HasSuffix(responseJID.String(), "@lid") && p.whatsapp != nil && p.whatsapp.Store != nil {
			contacts, err := p.whatsapp.Store.Contacts.GetAllContacts(ctx)
			if err != nil {
				logger.Error("Erro ao obter contatos: %v", err)
			} else {
				logger.Info("Encontrados %d contatos no armazenamento", len(contacts))

				// Percorrer os contatos para encontrar um que corresponda ao número do JID @lid
				for jid, contact := range contacts {
					// Verificar se o número de telefone do contato corresponde ao número no JID @lid
					if jid.User == chatJID.User {
						// Encontramos um contato com o mesmo número de telefone
						if !strings.HasSuffix(jid.String(), "@lid") {
							// Se o JID do contato não tem sufixo @lid, usá-lo
							responseJID = jid
							logger.Info("Encontrado contato com JID válido: %s", responseJID)
							break
						} else if contact.PushName != "" {
							// Se o contato tem um nome, registrar para debug
							logger.Info("Contato encontrado com nome: %s, mas ainda com JID @lid", contact.PushName)
						}
					}
				}

				// Se ainda não encontramos um JID válido, tentar pelo nome do contato
				if strings.HasSuffix(responseJID.String(), "@lid") {
					// Obter o nome do contato atual
					var currentName string
					for jid, contact := range contacts {
						if jid.User == chatJID.User && contact.PushName != "" {
							currentName = contact.PushName
							break
						}
					}

					// Se encontramos um nome, procurar por outros contatos com o mesmo nome
					if currentName != "" {
						logger.Info("Procurando contatos com o nome: %s", currentName)
						for jid, contact := range contacts {
							if contact.PushName == currentName && !strings.HasSuffix(jid.String(), "@lid") {
								responseJID = jid
								logger.Info("Encontrado contato pelo nome: %s -> %s", currentName, responseJID)
								break
							}
						}
					}
				}
			}
		}

		// Se ainda não encontramos o contato, tentar usar o JID do remetente da mensagem
		if strings.HasSuffix(responseJID.String(), "@lid") {
			logger.Info("Não foi possível encontrar o contato nos contatos armazenados")

			// Tentar usar o número do remetente da mensagem original
			if msg.Sender.String() != "" && !strings.HasSuffix(msg.Sender.String(), "@lid") {
				responseJID = msg.Sender
				logger.Info("Usando o JID do remetente: %s", responseJID)
			} else {
				// Último recurso: tentar extrair o número do JID original e usar com sufixo @s.whatsapp.net
				numberPart := strings.Split(chatJID.String(), "@")[0]
				responseJID, _ = types.ParseJID(numberPart + "@s.whatsapp.net")
				logger.Info("Último recurso: usando número extraído do JID original: %s", responseJID)
			}
		}
	}

	logger.Info("Sending response to JID: %s", responseJID)

	// Registrar informações adicionais da resposta para debug
	if response != nil {
		// Tentar extrair os detalhes se existirem
		if details, ok := response["details"].([]interface{}); ok && len(details) > 0 {
			logger.Info("Resposta contém %d detalhes", len(details))
		}

		// Tentar extrair listas se existirem
		if listsData, ok := response["lists"].(map[string]interface{}); ok && len(listsData) > 0 {
			logger.Info("Resposta contém listas")
		}
	}

	// Não enviar a mensagem diretamente aqui para evitar duplicação
	// Apenas preparar os dados da resposta para a função chamadora enviar
	logger.Info("Response prepared for user: %s", chatbotResp.Content)

	// Verificar se a resposta deve ser processada com base no campo 'handled' nos metadados
	logger.Info("[DEBUG] Verificando metadados da resposta: %+v", response)
	if metadata, ok := response["metadata"].(map[string]interface{}); ok {
		logger.Info("[DEBUG] Metadados encontrados: %+v", metadata)

		// Verificar o campo is_product_query
		if isProductQuery, ok := metadata["is_product_query"].(bool); ok {
			logger.Info("[DEBUG] Campo is_product_query encontrado: %v", isProductQuery)
			if !isProductQuery {
				logger.Info("[DEBUG] Não é consulta de produto (is_product_query=false). Não enviando resposta.")
				return nil, nil
			}
		}

		// Verificar o campo handled
		if handled, ok := metadata["handled"].(bool); ok {
			logger.Info("[DEBUG] Campo handled encontrado: %v", handled)
			if !handled {
				logger.Info("[DEBUG] Resposta com handled=false nos metadados. Não enviando resposta ao usuário.")
				// Retornar nil para indicar que não deve enviar resposta
				return nil, nil
			}
		} else {
			logger.Info("[DEBUG] Campo handled não encontrado ou não é boolean")
		}
	} else {
		logger.Info("[DEBUG] Metadados não encontrados na resposta")
	}

	// Serializar a estrutura ServiceResponse para retornar
	respBody, err := json.Marshal(chatbotResp)
	if err != nil {
		logger.Error("Failed to marshal ServiceResponse: %v", err)
		// Em caso de erro, retornar o body original
		return body, nil
	}

	return respBody, nil
}

// sendResponse sends the chatbot response back to the user
func (p *ForwarderPlugin) sendResponse(msg *policy.Message, response ServiceResponse) error {
	// Se a resposta estiver vazia, não enviar nada
	if response.Content == "" && len(response.Details) == 0 && response.Lists == nil {
		logger.Info("Resposta vazia, não enviando mensagem")
		return nil
	}
	if p.whatsapp == nil {
		return fmt.Errorf("WhatsApp client not initialized")
	}

	// Check if WhatsApp client is connected
	if !p.whatsapp.IsConnected() {
		return fmt.Errorf("WhatsApp client not connected")
	}

	// First check if we have sender_pn in metadata, which contains the actual phone number
	var targetJID types.JID = msg.Chat // Default to msg.Chat
	var err error

	// Check if we have sender_pn in metadata (this is present when message comes from mobile phone)
	if senderPNInterface, ok := msg.Metadata["sender_pn"]; ok {
		// Convert interface{} to string with type assertion
		if senderPN, ok := senderPNInterface.(string); ok {
			logger.Info("Found sender_pn in metadata: %s", senderPN)
			// Try to parse the sender_pn as a JID
			targetJID, err = types.ParseJID(senderPN)
			if err != nil {
				logger.Error("Failed to parse sender_pn as JID: %v", err)
				// Continue with the default JID
			} else {
				logger.Info("Using sender_pn for response: %s", targetJID.String())
				// Update the message's Chat JID
				msg.Chat = targetJID
			}
		} else {
			logger.Error("sender_pn metadata is not a string: %v", senderPNInterface)
		}
	} else {
		// If no sender_pn, check if the JID has the @lid suffix and convert it
		jidStr := msg.Chat.String()
		logger.Info("Original JID in sendResponse: %s", jidStr)

		if strings.HasSuffix(jidStr, "@lid") {
			// Extract the number part and convert to a standard WhatsApp JID
			numberPart := strings.Split(jidStr, "@")[0]
			logger.Info("Extracted number part: %s", numberPart)

			// Try to parse as a standard user JID
			targetJID, err = types.ParseJID(numberPart + "@s.whatsapp.net")
			if err != nil {
				logger.Error("Failed to parse JID: %v", err)
				return fmt.Errorf("invalid JID format: %v", err)
			}
			logger.Info("Converted JID: %s", targetJID.String())
			// Update the message's Chat JID
			msg.Chat = targetJID
		}
	}

	// Verificar se os metadados indicam que não devemos responder
	if metadata, ok := msg.Metadata["metadata"].(map[string]interface{}); ok {
		isProductQuery, hasProductFlag := metadata["is_product_query"].(bool)
		handled, hasHandledFlag := metadata["handled"].(bool)

		if (hasProductFlag && !isProductQuery) || (hasHandledFlag && !handled) {
			logger.Info("[DEBUG] Não respondendo baseado nos metadados da mensagem: is_product_query=%v, handled=%v",
				isProductQuery, handled)
			return fmt.Errorf("mensagem não deve ser respondida conforme metadados")
		}
	}

	// Format the response
	var responseText strings.Builder

	// Verificar se temos um campo "content" diretamente na estrutura ServiceResponse
	hasContent := response.Content != ""

	// Check if it's a list response
	if response.Lists != nil {
		// Add list title
		if response.Lists.Title != "" {
			responseText.WriteString("*" + response.Lists.Title + "*\n\n")
		}

		// Add list description
		if response.Lists.Description != "" {
			responseText.WriteString(response.Lists.Description + "\n\n")
		}

		// Add menu text
		if response.Lists.Menu != "" {
			responseText.WriteString(response.Lists.Menu + "\n\n")
		}

		// Add list items
		for _, list := range response.Lists.List {
			if list.Title != "" {
				responseText.WriteString("*" + list.Title + "*\n\n")
			}

			for i, row := range list.Rows {
				responseText.WriteString(fmt.Sprintf("%d. *%s*\n", i+1, row.Title))
				if row.Description != "" {
					responseText.WriteString(fmt.Sprintf("   %s\n", row.Description))
				}
			}
		}

		// Send the text message
		message := &waProto.Message{
			Conversation: proto.String(responseText.String()),
		}
		_, err := p.whatsapp.SendMessage(context.Background(), msg.Chat, message)
		if err != nil {
			return fmt.Errorf("failed to send WhatsApp message: %v", err)
		}
	} else if len(response.Details) > 0 {
		// Format product details
		// First check if we have valid product details
		validProducts := 0
		for _, detail := range response.Details {
			if detail.Image.Text != "" || detail.Desc.Text != "" {
				validProducts++
			}
		}

		if validProducts == 0 {
			// No valid products found, send a generic message
			responseText.WriteString("Encontrados " + fmt.Sprintf("%d", len(response.Details)) + " produtos relacionados à sua consulta, mas não foi possível exibir os detalhes.")
			message := &waProto.Message{
				Conversation: proto.String(responseText.String()),
			}
			_, err := p.whatsapp.SendMessage(context.Background(), msg.Chat, message)
			if err != nil {
				return fmt.Errorf("failed to send WhatsApp message: %v", err)
			}
			return nil
		}

		// We have valid products, format them
		responseText.WriteString("Encontrados " + fmt.Sprintf("%d", validProducts) + " produtos relacionados à sua consulta.\n\n")

		for i, detail := range response.Details {
			// Skip invalid products
			if detail.Image.Text == "" && detail.Desc.Text == "" {
				continue
			}

			if i > 0 {
				responseText.WriteString("\n\n---\n\n")
			}

			// Add product number
			responseText.WriteString(fmt.Sprintf("%d. ", i+1))

			// Add product name
			if detail.Image.Text != "" {
				responseText.WriteString("*" + detail.Image.Text + "*\n\n")
			}

			// Add description
			if detail.Desc.Text != "" {
				responseText.WriteString(detail.Desc.Text)
			}

			// Send the text message first
			message := &waProto.Message{
				Conversation: proto.String(responseText.String()),
			}
			_, err := p.whatsapp.SendMessage(context.Background(), msg.Chat, message)
			if err != nil {
				return fmt.Errorf("failed to send WhatsApp message: %v", err)
			}

			// If there's an image URL, send it as an image message
			if detail.Image.URL != "" {
				// Download the image
				resp, err := http.Get(detail.Image.URL)
				if err != nil {
					logger.Error("Failed to download image: %v", err)
					continue
				}
				defer resp.Body.Close()

				// Read the image data
				imageData, err := io.ReadAll(resp.Body)
				if err != nil {
					logger.Error("Failed to read image data: %v", err)
					continue
				}

				// Create the image message
				imageMsg := &waProto.Message{
					ImageMessage: &waProto.ImageMessage{
						URL:           proto.String(detail.Image.URL),
						Mimetype:      proto.String("image/jpeg"),
						Caption:       proto.String(detail.Image.Text),
						FileLength:    proto.Uint64(uint64(len(imageData))),
						DirectPath:    proto.String(""),
						MediaKey:      []byte{},
						FileEncSHA256: []byte{},
						FileSHA256:    []byte{},
					},
				}

				// Upload and send the image
				uploaded, err := p.whatsapp.Upload(context.Background(), imageData, whatsmeow.MediaImage)
				if err != nil {
					logger.Error("Failed to upload image: %v", err)
					continue
				}

				imageMsg.ImageMessage.URL = proto.String(uploaded.URL)
				imageMsg.ImageMessage.DirectPath = proto.String(uploaded.DirectPath)
				imageMsg.ImageMessage.MediaKey = uploaded.MediaKey
				imageMsg.ImageMessage.FileEncSHA256 = uploaded.FileEncSHA256
				imageMsg.ImageMessage.FileSHA256 = uploaded.FileSHA256

				_, err = p.whatsapp.SendMessage(context.Background(), msg.Chat, imageMsg)
				if err != nil {
					logger.Error("Failed to send image message: %v", err)
				}
			}

			// Reset the response text for the next product
			responseText.Reset()
		}
	} else if hasContent {
		// Se temos o campo "content", usá-lo diretamente como resposta
		responseText.WriteString(response.Content)
		message := &waProto.Message{
			Conversation: proto.String(responseText.String()),
		}
		logger.Info("Sending content response to user: %s", responseText.String())
		_, err := p.whatsapp.SendMessage(context.Background(), msg.Chat, message)
		if err != nil {
			return fmt.Errorf("failed to send WhatsApp message: %v", err)
		}
	} else {
		responseText.WriteString("Nenhum resultado encontrado.")
		message := &waProto.Message{
			Conversation: proto.String(responseText.String()),
		}
		_, err := p.whatsapp.SendMessage(context.Background(), msg.Chat, message)
		if err != nil {
			return fmt.Errorf("failed to send WhatsApp message: %v", err)
		}
	}

	// Log the outgoing message if message logger is available
	if p.msgLogger != nil && responseText.Len() > 0 {
		// Create metadata JSON
		metadata := map[string]interface{}{
			"chat_id":          msg.Chat.String(),
			"recipient":        msg.Sender.String(),
			"is_interactive":   response.Lists != nil || len(response.Details) > 0,
			"original_message": msg.Content, // Adicionando a mensagem original que gerou a resposta
		}

		// Add AI metadata if available
		if response.Metadata != nil {
			if aiGenerated, ok := response.Metadata["ai_generated"].(bool); ok {
				metadata["ai_generated"] = aiGenerated
			}
			if handled, ok := response.Metadata["handled"].(bool); ok {
				metadata["handled"] = handled
			}
			if isProductQuery, ok := response.Metadata["is_product_query"].(bool); ok {
				metadata["is_product_query"] = isProductQuery
			}
		}

		metadataJSON, _ := json.Marshal(metadata)

		// Verificar o formato do ID da sessão
		sessionIDStr := p.whatsapp.Store.ID.String()
		logger.Info("[ForwarderPlugin] ID da sessão original (resposta do bot): %s", sessionIDStr)

		// Extrair o número de telefone do ID da sessão
		// O formato pode ser algo como "1234567890.0:2" ou apenas "1234567890"
		phoneNumber := sessionIDStr
		if parts := strings.Split(sessionIDStr, "."); len(parts) > 0 {
			phoneNumber = parts[0]
		}
		logger.Info("[ForwarderPlugin] Número de telefone extraído (resposta do bot): %s", phoneNumber)

		// Create message log entry - usar o número de telefone como ID da sessão
		msgLog := &msglog.Message{
			SessionID:   phoneNumber,
			UserID:      msg.Sender.String(),
			Content:     responseText.String(),
			Direction:   msglog.DirectionOutgoing,
			Timestamp:   time.Now(),
			MessageType: "text",
			Metadata:    string(metadataJSON),
		}

		// Log the message asynchronously to not block message processing
		go func() {
			if err := p.msgLogger.LogMessage(context.Background(), msgLog); err != nil {
				logger.Error("Failed to log outgoing message: %v", err)
			} else {
				logger.Info("Successfully logged outgoing message with ID: %s", msgLog.ID)
			}
		}()
	}

	logger.Info("Response sent to user: %s", responseText.String())
	return nil
}

// handleForwardCommand handles the !forward command
func (p *ForwarderPlugin) handleForwardCommand(ctx context.Context, cmdCtx *handler.CommandContext) error {
	message := fmt.Sprintf("Message forwarding is enabled. URL: %s", p.forwardURL)
	return p.CmdHandler.SendTextMessage(ctx, cmdCtx.Chat, message)
}

// registerSelectionHandler registers a handler for numeric selections in a chat
func (p *ForwarderPlugin) registerSelectionHandler(chatJID types.JID, rowIDMap map[string]string) {
	chatID := chatJID.String()

	// Store the selection handler for this chat
	p.selectionHandlerMutex.Lock()
	defer p.selectionHandlerMutex.Unlock()

	// Create a new map for this chat if it doesn't exist
	p.selectionHandlers[chatID] = rowIDMap

	// Set up a cleanup timer to remove the handler after 30 minutes
	go func() {
		time.Sleep(30 * time.Minute)
		p.selectionHandlerMutex.Lock()
		delete(p.selectionHandlers, chatID)
		p.selectionHandlerMutex.Unlock()
	}()
}

// GetHandleForwardCommand returns the handler function for the !forward command for testing
func (p *ForwarderPlugin) GetHandleForwardCommand() func(context.Context, *handler.CommandContext) error {
	return p.handleForwardCommand
}

// GetPolicy returns the handler's policy
func (p *ForwarderPlugin) GetPolicy() *policy.HandlerPolicy {
	return &policy.HandlerPolicy{
		Name:     p.policy.Name,
		Enabled:  p.policy.Enabled,
		Priority: p.policy.Priority,
		Patterns: []policy.Pattern{
			{Type: StringPtr("text")},
			{Type: StringPtr("image")},
			{Type: StringPtr("video")},
		},
		RateLimit: policy.RateLimit{
			Enabled:           true,
			RequestsPerMinute: 30,
			Burst:             5,
		},
	}
}
